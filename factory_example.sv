// Factory Pattern Implementation in SystemVerilog
// 演示UVM Factory的核心实现原理

// 基础对象类 - 所有可创建对象的基类
virtual class base_object;
    string type_name;
    
    function new(string name = "base_object");
        this.type_name = name;
    endfunction
    
    // 虚函数，用于多态
    virtual function void print_info();
        $display("Base Object: %s", type_name);
    endfunction
    
    // 克隆函数 - Factory需要的核心功能
    virtual function base_object clone();
        base_object obj = new();
        obj.type_name = this.type_name;
        return obj;
    endfunction
endclass

// 具体实现类A
class packet_a extends base_object;
    int data_a;
    
    function new(string name = "packet_a");
        super.new(name);
        this.data_a = 100;
    endfunction
    
    virtual function void print_info();
        $display("Packet A: %s, data_a = %0d", type_name, data_a);
    endfunction
    
    virtual function base_object clone();
        packet_a obj = new();
        obj.type_name = this.type_name;
        obj.data_a = this.data_a;
        return obj;
    endfunction
endclass

// 具体实现类B
class packet_b extends base_object;
    string data_b;
    
    function new(string name = "packet_b");
        super.new(name);
        this.data_b = "Hello";
    endfunction
    
    virtual function void print_info();
        $display("Packet B: %s, data_b = %s", type_name, data_b);
    endfunction
    
    virtual function base_object clone();
        packet_b obj = new();
        obj.type_name = this.type_name;
        obj.data_b = this.data_b;
        return obj;
    endfunction
endclass

// Factory类 - 核心工厂实现
class object_factory;
    // 静态关联数组，存储类型名到原型对象的映射
    static base_object prototypes[string];
    
    // 注册函数 - 将类型和原型对象关联
    static function void register_type(string type_name, base_object prototype);
        prototypes[type_name] = prototype;
        $display("Registered type: %s", type_name);
    endfunction
    
    // 创建函数 - 根据类型名创建对象
    static function base_object create(string type_name);
        if (prototypes.exists(type_name)) begin
            base_object obj = prototypes[type_name].clone();
            $display("Created object of type: %s", type_name);
            return obj;
        end else begin
            $error("Unknown type: %s", type_name);
            return null;
        end
    endfunction
    
    // 覆盖函数 - 运行时替换类型实现
    static function void override_type(string original_type, string new_type);
        if (prototypes.exists(new_type)) begin
            prototypes[original_type] = prototypes[new_type];
            $display("Override: %s -> %s", original_type, new_type);
        end else begin
            $error("Cannot override with unknown type: %s", new_type);
        end
    endfunction
    
    // 显示所有注册的类型
    static function void show_registered_types();
        string type_names[$];
        $display("=== Registered Types ===");
        foreach (prototypes[key]) begin
            type_names.push_back(key);
        end
        type_names.sort();
        foreach (type_names[i]) begin
            $display("  %s", type_names[i]);
        end
    endfunction
endclass

// 测试模块
module factory_test;
    initial begin
        base_object obj1, obj2, obj3;
        packet_a proto_a;
        packet_b proto_b;
        
        $display("=== Factory Pattern Demo ===\n");
        
        // 1. 创建原型对象并注册到工厂
        $display("1. Registering prototypes...");
        proto_a = new("packet_a");
        proto_b = new("packet_b");
        
        object_factory::register_type("packet_a", proto_a);
        object_factory::register_type("packet_b", proto_b);
        
        // 显示注册的类型
        object_factory::show_registered_types();
        $display("");
        
        // 2. 使用工厂创建对象
        $display("2. Creating objects using factory...");
        obj1 = object_factory::create("packet_a");
        obj2 = object_factory::create("packet_b");
        
        if (obj1 != null) obj1.print_info();
        if (obj2 != null) obj2.print_info();
        $display("");
        
        // 3. 演示类型覆盖功能
        $display("3. Demonstrating type override...");
        object_factory::override_type("packet_a", "packet_b");
        
        obj3 = object_factory::create("packet_a");  // 现在会创建packet_b类型
        if (obj3 != null) obj3.print_info();
        $display("");
        
        // 4. 尝试创建未注册的类型
        $display("4. Trying to create unregistered type...");
        obj1 = object_factory::create("packet_c");
        $display("");
        
        $display("=== Demo Complete ===");
        $finish;
    end
endmodule
